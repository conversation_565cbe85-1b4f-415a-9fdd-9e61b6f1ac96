/**
 * 主播统计数据管理Hook
 * 
 * 提供主播统计数据和首充统计数据的查询功能
 */

import { useState, useCallback, useEffect } from 'react'
import { toast } from '@/hooks'
import { OperationsService } from '@/services/operations'
import type { AnchorStatsResponse, FirstRechargeStatsResponse } from '../types/operations'
import { PERMISSIONS } from '@/constants/permissions'

/**
 * 主播统计Hook状态接口
 */
interface UseAnchorStatsState {
  /** 主播统计数据 */
  statsData: AnchorStatsResponse | null
  /** 首充统计数据 */
  firstRechargeData: FirstRechargeStatsResponse | null
  /** 统计数据加载状态 */
  statsLoading: boolean
  /** 首充数据加载状态 */
  firstRechargeLoading: boolean
  /** 错误信息 */
  error: string | null
  /** 当前主播ID */
  anchorId: number | null
  /** 时间范围 */
  timeRange: {
    startTime?: number
    endTime?: number
  }
}

/**
 * 主播统计数据管理Hook
 * 
 * 提供主播统计数据和首充统计数据的查询功能
 */
export function useAnchorStats() {
  const [state, setState] = useState<UseAnchorStatsState>({
    statsData: null,
    firstRechargeData: null,
    statsLoading: false,
    firstRechargeLoading: false,
    error: null,
    anchorId: null,
    timeRange: {}
  })
  
  /**
   * 加载主播统计数据
   */
  const loadStats = useCallback(async (
    anchorId: number,
    startTime?: number,
    endTime?: number
  ) => {
    setState(prev => ({ 
      ...prev, 
      statsLoading: true, 
      error: null,
      anchorId,
      timeRange: { startTime, endTime }
    }))
    
    try {
      const data = await OperationsService.getAnchorStats(anchorId, startTime, endTime)
      
      setState(prev => ({
        ...prev,
        statsData: data,
        statsLoading: false
      }))
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取主播统计数据失败'
      setState(prev => ({
        ...prev,
        statsData: null,
        statsLoading: false,
        error: errorMessage
      }))
      toast({
        title: errorMessage,
        variant: 'destructive'
      })
    }
  }, [])
  
  /**
   * 加载首充统计数据
   */
  const loadFirstRechargeStats = useCallback(async (
    anchorId: number,
    startTime?: number,
    endTime?: number
  ) => {
    setState(prev => ({ 
      ...prev, 
      firstRechargeLoading: true, 
      error: null,
      anchorId,
      timeRange: { startTime, endTime }
    }))
    
    try {
      const data = await OperationsService.getFirstRechargeStats(anchorId, startTime, endTime)
      
      setState(prev => ({
        ...prev,
        firstRechargeData: data,
        firstRechargeLoading: false
      }))
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取首充统计数据失败'
      setState(prev => ({
        ...prev,
        firstRechargeData: null,
        firstRechargeLoading: false,
        error: errorMessage
      }))
      toast({
        title: errorMessage,
        variant: 'destructive'
      })
    }
  }, [])
  
  /**
   * 同时加载主播统计和首充统计数据
   */
  const loadAllStats = useCallback(async (
    anchorId: number,
    startTime?: number,
    endTime?: number
  ) => {
    await Promise.all([
      loadStats(anchorId, startTime, endTime),
      loadFirstRechargeStats(anchorId, startTime, endTime)
    ])
  }, [loadStats, loadFirstRechargeStats])
  
  /**
   * 刷新当前数据
   */
  const refreshStats = useCallback(() => {
    if (state.anchorId) {
      loadAllStats(
        state.anchorId,
        state.timeRange.startTime,
        state.timeRange.endTime
      )
    }
  }, [state.anchorId, state.timeRange.startTime, state.timeRange.endTime, loadAllStats])
  
  /**
   * 设置时间范围
   */
  const setTimeRange = useCallback((startTime?: number, endTime?: number) => {
    setState(prev => ({
      ...prev,
      timeRange: { startTime, endTime }
    }))
  }, [])
  
  /**
   * 重置数据
   */
  const reset = useCallback(() => {
    setState({
      statsData: null,
      firstRechargeData: null,
      statsLoading: false,
      firstRechargeLoading: false,
      error: null,
      anchorId: null,
      timeRange: {}
    })
  }, [])
  
  // 当主播ID或时间范围变化时自动加载数据
  useEffect(() => {
    if (state.anchorId) {
      loadAllStats(
        state.anchorId,
        state.timeRange.startTime,
        state.timeRange.endTime
      )
    }
  }, [state.anchorId, state.timeRange.startTime, state.timeRange.endTime, loadAllStats])
  
  return {
    ...state,
    loadStats,
    loadFirstRechargeStats,
    loadAllStats,
    refreshStats,
    setTimeRange,
    reset
  }
}

/**
 * 统计数据格式化Hook
 * 
 * 提供统计数据的格式化和计算功能
 */
export function useStatsFormatter() {
  
  /**
   * 格式化统计卡片数据
   */
  const formatStatsCards = useCallback((stats: AnchorStatsResponse | null) => {
    if (!stats) return []
    
    return [
      {
        title: '总充值金额',
        value: `¥${stats.totalRecharge?.toLocaleString() || '0'}`,
        description: '累计充值总额',
        trend: stats.periodTotalRecharge ? {
          value: stats.periodTotalRecharge,
          label: '时间区间内'
        } : undefined
      },
      {
        title: '总消费金额',
        value: `¥${stats.totalConsume?.toLocaleString() || '0'}`,
        description: '累计消费总额'
      },
      {
        title: '用户总数',
        value: stats.userCount?.toLocaleString() || '0',
        description: '下级用户总数',
        trend: stats.periodNewUserCount ? {
          value: stats.periodNewUserCount,
          label: '时间区间新增'
        } : undefined
      },
      {
        title: '实际利润',
        value: `¥${stats.actualProfit?.toLocaleString() || '0'}`,
        description: '实际利润金额',
        highlight: true
      }
    ]
  }, [])
  
  /**
   * 格式化首充统计卡片数据
   */
  const formatFirstRechargeCards = useCallback((stats: FirstRechargeStatsResponse | null) => {
    if (!stats) return []
    
    return [
      {
        title: '首充用户数',
        value: stats.firstRechargeUserCount?.toLocaleString() || '0',
        description: '已首充用户总数',
        trend: stats.periodFirstRechargeUserCount ? {
          value: stats.periodFirstRechargeUserCount,
          label: '时间区间内'
        } : undefined
      },
      {
        title: '首充转化率',
        value: `${stats.firstRechargeConversionRate?.toFixed(2) || '0'}%`,
        description: '首充用户占比'
      },
      {
        title: '首充总金额',
        value: `¥${stats.totalFirstRechargeAmount?.toLocaleString() || '0'}`,
        description: '累计首充金额',
        trend: stats.periodFirstRechargeAmount ? {
          value: stats.periodFirstRechargeAmount,
          label: '时间区间内'
        } : undefined
      },
      {
        title: '平均首充金额',
        value: `¥${stats.avgFirstRechargeAmount?.toLocaleString() || '0'}`,
        description: '单用户平均首充',
        trend: stats.periodAvgFirstRechargeAmount ? {
          value: stats.periodAvgFirstRechargeAmount,
          label: '时间区间内'
        } : undefined
      }
    ]
  }, [])
  
  return {
    formatStatsCards,
    formatFirstRechargeCards
  }
}
